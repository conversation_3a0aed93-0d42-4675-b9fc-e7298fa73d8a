# Configuración de Base de Datos
DATABASE_URL=postgresql://postgres:123456@localhost:5432/workflow_db
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=123456
DB_NAME=workflow_db

# Configuración de la Aplicación
APP_PORT=8000
APP_ENV=development

# Configuración de Seguridad
SESSION_SECRET=tu_clave_secreta_muy_larga_y_segura_para_sesiones_12345

# Configuración del Usuario Administrador por Defecto
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123
ADMIN_NAME=Administrador del Sistema

# Configuración de Desarrollo
DEBUG=true

# Configuración de CORS
CORS_ORIGIN=http://localhost:8000
