// User model
export interface User {
  id: number;
  name: string;
  email: string;
  password: string;
  role: string;
  createdAt: Date;
  updatedAt: Date;
}

// User creation DTO
export interface CreateUserDTO {
  name: string;
  email: string;
  password: string;
  role?: string;
}

// User update DTO
export interface UpdateUserDTO {
  name?: string;
  email?: string;
  password?: string;
  role?: string;
}