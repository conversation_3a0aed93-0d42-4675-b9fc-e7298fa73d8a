// Task model
export interface Task {
  id: number;
  title: string;
  description?: string;
  sprintId?: number;
  assigneeId?: number;
  status: string;
  priority: string;
  storyPoints?: number;
  createdAt: Date;
  updatedAt: Date;
}

// Comment model
export interface Comment {
  id: number;
  content: string;
  taskId: number;
  userId: number;
  createdAt: Date;
  updatedAt: Date;
}

// Task creation DTO
export interface CreateTaskDTO {
  title: string;
  description?: string;
  sprintId?: number;
  assigneeId?: number;
  status?: string;
  priority?: string;
  storyPoints?: number;
}

// Task update DTO
export interface UpdateTaskDTO {
  title?: string;
  description?: string;
  sprintId?: number;
  assigneeId?: number;
  status?: string;
  priority?: string;
  storyPoints?: number;
}

// Comment creation DTO
export interface CreateCommentDTO {
  content: string;
  taskId: number;
  userId: number;
}

// Comment update DTO
export interface UpdateCommentDTO {
  content?: string;
}