// Team model
export interface Team {
  id: number;
  name: string;
  projectId: number;
  createdAt: Date;
  updatedAt: Date;
}

// Team member model
export interface TeamMember {
  id: number;
  teamId: number;
  userId: number;
  role: string;
  createdAt: Date;
  updatedAt: Date;
}

// Team creation DTO
export interface CreateTeamDTO {
  name: string;
  projectId: number;
}

// Team update DTO
export interface UpdateTeamDTO {
  name?: string;
  projectId?: number;
}

// Team member creation DTO
export interface AddTeamMemberDTO {
  teamId: number;
  userId: number;
  role?: string;
}

// Team member update DTO
export interface UpdateTeamMemberDTO {
  role?: string;
}