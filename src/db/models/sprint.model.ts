// Sprint model
export interface Sprint {
  id: number;
  name: string;
  description?: string;
  projectId: number;
  startDate: Date;
  endDate: Date;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

// Sprint creation DTO
export interface CreateSprintDTO {
  name: string;
  description?: string;
  projectId: number;
  startDate: Date;
  endDate: Date;
  status?: string;
}

// Sprint update DTO
export interface UpdateSprintDTO {
  name?: string;
  description?: string;
  startDate?: Date;
  endDate?: Date;
  status?: string;
}