import { testConnection, closePool } from "../../db/db.ts";

async function main() {
  console.log("Testing database connection...");
  
  try {
    const success = await testConnection();
    if (success) {
      console.log("Database connection test successful!");
    } else {
      console.error("Database connection test failed!");
    }
  } catch (error) {
    console.error("Database connection test failed:", error);
  } finally {
    await closePool();
  }
}

if (import.meta.main) {
  main();
}
