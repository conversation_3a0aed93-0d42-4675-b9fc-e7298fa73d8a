/**
 * Script para actualizar automáticamente las importaciones después de la reorganización
 */

const importMappings = {
  // Database imports
  '"../../../infrastructure/database/': '"../../../infrastructure/database/',
  '"../../infrastructure/database/': '"../../infrastructure/database/',
  '"../infrastructure/database/': '"../infrastructure/database/',
  
  // Utils imports
  '"../../../shared/utils/': '"../../../shared/utils/',
  '"../../shared/utils/': '"../../shared/utils/',
  '"../shared/utils/': '"../shared/utils/',
  
  // Components imports
  '"../../../shared/components/': '"../../../shared/components/',
  '"../../shared/components/': '"../../shared/components/',
  '"../shared/components/': '"../shared/components/',
  
  // Islands imports
  '"../../../shared/components/': '"../../../shared/components/',
  '"../../shared/components/': '"../../shared/components/',
  '"../shared/components/': '"../shared/components/',
  
  // Models imports
  '"../../../shared/types/': '"../../../shared/types/',
  '"../../shared/types/': '"../../shared/types/',
  '"../shared/types/': '"../shared/types/',
};

async function updateImportsInFile(filePath: string) {
  try {
    let content = await Deno.readTextFile(filePath);
    let updated = false;
    
    for (const [oldPath, newPath] of Object.entries(importMappings)) {
      if (content.includes(oldPath)) {
        content = content.replaceAll(oldPath, newPath);
        updated = true;
      }
    }
    
    if (updated) {
      await Deno.writeTextFile(filePath, content);
      console.log(`✅ Updated imports in: ${filePath}`);
    }
  } catch (error) {
    console.log(`❌ Error updating ${filePath}:`, error.message);
  }
}

async function findAndUpdateFiles(dir: string) {
  try {
    for await (const entry of Deno.readDir(dir)) {
      const fullPath = `${dir}/${entry.name}`;
      
      if (entry.isDirectory) {
        await findAndUpdateFiles(fullPath);
      } else if (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx')) {
        await updateImportsInFile(fullPath);
      }
    }
  } catch (error) {
    console.log(`❌ Error reading directory ${dir}:`, error.message);
  }
}

async function fixImports() {
  console.log("🔧 Actualizando importaciones...");
  
  const directories = [
    "src/features",
    "src/shared",
    "src/infrastructure",
    "src/app"
  ];
  
  for (const dir of directories) {
    console.log(`📁 Procesando directorio: ${dir}`);
    await findAndUpdateFiles(dir);
  }
  
  console.log("🎉 ¡Importaciones actualizadas!");
}

if (import.meta.main) {
  await fixImports();
}
