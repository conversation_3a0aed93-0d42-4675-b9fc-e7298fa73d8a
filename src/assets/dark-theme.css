/* Estilos específicos para el tema oscuro */
.dark {
  background-color: #1a202c !important;
  color: #e2e8f0 !important;
}

.dark body {
  background-color: #1a202c !important;
  color: #e2e8f0 !important;
}

/* Estilos específicos para el tema claro (para forzar el cambio) */
html:not(.dark) {
  background-color: #ffffff !important;
  color: #1a202c !important;
}

html:not(.dark) body {
  background-color: #ffffff !important;
  color: #1a202c !important;
}

html:not(.dark) .bg-gray-50 {
  background-color: #f9fafb !important;
}

html:not(.dark) .bg-gray-100 {
  background-color: #f3f4f6 !important;
}

html:not(.dark) .bg-white {
  background-color: #ffffff !important;
}

.dark .bg-white {
  background-color: #2d3748 !important;
}

.dark .bg-gray-50,
.dark .min-h-screen.bg-gray-50 {
  background-color: #1a202c !important;
}

/* Estilos específicos para el dashboard en modo claro */
html:not(.dark) .min-h-screen.bg-gray-50 {
  background-color: #f9fafb !important;
}

.dark .bg-gray-100 {
  background-color: #2d3748 !important;
}

.dark .text-gray-700 {
  color: #e2e8f0 !important;
}

.dark .text-gray-500 {
  color: #a0aec0 !important;
}

.dark .border-gray-200 {
  border-color: #4a5568 !important;
}

.dark .hover\:bg-gray-50:hover {
  background-color: #4a5568 !important;
}

.dark .hover\:bg-gray-100:hover {
  background-color: #4a5568 !important;
}

/* Estilos para el panel lateral en modo oscuro */
.dark .bg-blue-100 {
  background-color: #2c5282 !important;
}

.dark .text-blue-600 {
  color: #63b3ed !important;
}

.dark .hover\:bg-blue-50:hover {
  background-color: #2a4365 !important;
}

.dark .hover\:text-blue-600:hover {
  color: #63b3ed !important;
}

/* Estilos para botones en modo oscuro */
.dark .bg-blue-600 {
  background-color: #3182ce !important;
}

.dark .hover\:bg-blue-700:hover {
  background-color: #2b6cb0 !important;
}

/* Estilos para inputs en modo oscuro */
.dark input,
.dark select,
.dark textarea {
  background-color: #2d3748 !important;
  color: #e2e8f0 !important;
  border-color: #4a5568 !important;
}

.dark input::placeholder {
  color: #a0aec0 !important;
}

/* Estilos para modales en modo oscuro */
.dark .bg-gray-800 {
  background-color: #1a202c !important;
}

.dark .bg-gray-700 {
  background-color: #2d3748 !important;
}

/* Estilos para tarjetas en modo oscuro */
.dark .shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.3) !important;
}

/* Estilos para tablas en modo oscuro */
.dark .bg-gray-50 th {
  background-color: #2d3748 !important;
  color: #e2e8f0 !important;
}

.dark .divide-gray-200 > * + * {
  border-color: #4a5568 !important;
}

/* Estilos para notificaciones en modo oscuro */
.dark .bg-green-50 {
  background-color: #1c4532 !important;
}

.dark .bg-red-50 {
  background-color: #742a2a !important;
}

.dark .text-green-700 {
  color: #9ae6b4 !important;
}

.dark .text-red-700 {
  color: #feb2b2 !important;
}

.dark .border-green-200 {
  border-color: #276749 !important;
}

.dark .border-red-200 {
  border-color: #9b2c2c !important;
}
