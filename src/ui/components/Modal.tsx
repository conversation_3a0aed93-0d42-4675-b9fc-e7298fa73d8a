import { JSX } from "preact";
import ModalIsland from "../islands/ModalIsland.tsx";

interface ModalProps {
  show: boolean;
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl";
  closeable?: boolean;
  onClose: () => void;
  title?: string;
  children: JSX.Element | JSX.Element[];
}

// Este componente es un wrapper para ModalIsland
// Permite usar el componente Modal en los componentes importados
export default function Modal({
  show,
  maxWidth = "2xl",
  closeable = true,
  onClose,
  title,
  children,
}: ModalProps) {
  return (
    <ModalIsland
      show={show}
      maxWidth={maxWidth}
      closeable={closeable}
      onClose={onClose}
    >
      {title && (
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        </div>
      )}
      {children}
    </ModalIsland>
  );
}