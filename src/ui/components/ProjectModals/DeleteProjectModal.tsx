import { Project } from "../../ui/models/project.ts";
import Modal from "../Modal.tsx";

interface DeleteProjectModalProps {
  show: boolean;
  project: Project | null;
  onClose: () => void;
  onSuccess: () => void;
}

export default function DeleteProjectModal({ show, project, onClose, onSuccess }: DeleteProjectModalProps) {
  if (!project) return null;

  const handleDelete = async () => {
    try {
      const response = await fetch(`/api/projects/${project.id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        onSuccess();
      } else {
        const error = await response.json();
        console.error('Error al eliminar proyecto:', error);
        alert(`Error al eliminar proyecto: ${error.error || 'Error desconocido'}`);
      }
    } catch (error) {
      console.error('Error al eliminar proyecto:', error);
      alert('Error al eliminar proyecto. Por favor, inténtalo de nuevo.');
    }
  };

  return (
    <Modal show={show} onClose={onClose} title="Eliminar Proyecto">
      <div class="p-6">
        <div class="mb-6">
          <p class="text-red-600 font-semibold mb-2">¿Estás seguro de que deseas eliminar este proyecto?</p>
          <p class="text-gray-600">Esta acción no se puede deshacer y eliminará todos los datos asociados al proyecto.</p>
          <div class="mt-4 p-4 bg-gray-100 rounded-md">
            <p class="font-semibold">{project.name}</p>
            <p class="text-sm text-gray-600">{project.description}</p>
          </div>
        </div>

        <div class="flex justify-end space-x-3">
          <button
            type="button"
            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            onClick={onClose}
          >
            Cancelar
          </button>
          <button
            type="button"
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            onClick={handleDelete}
          >
            Eliminar Proyecto
          </button>
        </div>
      </div>
    </Modal>
  );
}